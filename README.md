# NeXus Documentation Hub

This is the central documentation for the NeXus team collaboration.

## Local Development

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev
```

## Deployment

### Environment Variables

The site URL is configured using environment variables with the following priority:
1. `CF_PAGES_URL` - Automatically injected by Cloudflare Pages during deployment
2. `SITE_URL` - Can be manually set for other platforms
3. Defaults to `http://localhost:4321` for local development

### Deploying to Cloudflare Pages

When deploying to Cloudflare Pages:

1. Connect your repository to Cloudflare Pages
2. Set the build command to `pnpm build`
3. Set the build output directory to `dist`
4. No need to manually set `SITE_URL` as <PERSON>flare automatically injects `CF_PAGES_URL`

For direct deployment using Wrangler:
```bash
pnpm cf-deploy
```

### Deploying to Vercel

When deploying to Vercel:

1. Connect your repository to Vercel
2. Set the framework preset to Astro
3. Set the build command to `pnpm build`
4. Set the output directory to `dist`
5. No need to manually set `SITE_URL` as our configuration will automatically use `VERCEL_URL` if available

## Documentation Structure

- **Onboarding:** Essential information for getting started with the team and our tools.
- **Business:** Context on our goals, strategies, and market approach.
- **Development:** Guides, standards, and information about our tech stack.
- **Operations:** Details on our infrastructure, deployment, and DevOps practices.

```pnpm create astro@latest -- --template starlight
```