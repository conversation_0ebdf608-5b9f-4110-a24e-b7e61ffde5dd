// @ts-check
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

// Get environment variables with fallbacks
// Check for platform-specific URLs first (Cloudflare, Vercel)
const SITE_URL = 
  process.env.CF_PAGES_URL || 
  (process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 
    (process.env.SITE_URL || 'http://localhost:4321'));

// https://astro.build/config
export default defineConfig({
	site: SITE_URL,
	integrations: [
		starlight({
			title: 'nexus team docs',
			social: [{ icon: 'github', label: 'GitHub', href: 'https://github.com/shipnexus/nexus-docs' }],
			sidebar: [
			     // Group 1: Onboarding - Autogenerate content from 'onboarding' directory
			     {
			       label: '🚀 Onboarding',
			       autogenerate: { directory: 'onboarding' }
			     },
			     // Group 2: Business - Autogenerate content from 'business' directory
			     {
			       label: '💡 Business',
			       autogenerate: { directory: 'business' }
			     },
			     // Group 3: Development - Autogenerate content from 'development' directory
			     // Starlight will automatically handle the 'frontend', 'llm-ai', 'aidev' subdirs
			     {
			       label: '💻 Development',
			       autogenerate: { directory: 'development' }
			     },
			     // Group 4: Operations - Autogenerate content from 'operations' directory
			     {
			       label: '☁️ Operations',
			       autogenerate: { directory: 'operations' }
			     },
			   ],
		}),
	],
});
