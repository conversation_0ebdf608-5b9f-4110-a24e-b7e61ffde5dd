---
title: Development Environment Setup
description: Setting up your Mac for NeXus development.
sidebar:
  order: 2
---

# Setting Up Your Development Environment

This guide covers the necessary tools and configurations for developing NeXus projects on macOS.

## Core Tools

-   Homebrew
-   Git
-   Node.js (via nvm or fnm)
-   VS Code + Recommended Extensions
-   Docker (if applicable)

## API Keys & Secrets Management

(Details on how keys are shared and managed will go here - e.g., 1Password, Doppler, .env files)

## Project Setup

(Instructions for cloning repositories, installing dependencies, etc.)