---
title: CI/CD Pipeline
description: Overview of the NeXus continuous integration and deployment process.
---

# Continuous Integration & Deployment (CI/CD)

This document describes our CI/CD workflow.

## Tools

-   GitHub Actions (Primary)
-   Cloudflare Pages/Workers Deployment Integration

## Workflow

-   Branching Strategy (e.g., Gitflow)
-   Automated Testing
-   Build Process
-   Deployment Stages (Staging, Production)
-   - Serverless Deployment Patterns (See [Serverless Guides](/development/serverless/))

## Access & Permissions

(Details on who manages deployments)
