---
title: Infrastructure Overview
description: Details on NeXus cloud resources and Infrastructure as Code (IaC).
---

# Infrastructure

This document outlines our cloud infrastructure setup and management practices.

## Cloud Providers

-   Cloudflare (Primary)
-   AWS (Specific services)

## Infrastructure as Code (IaC)

-   Terraform (Multi-cloud provisioning)
-   Wrangler (Cloudflare-specific config)
-   Configuration Management:
    - Environment variables via Cloudflare Secrets
    - Vercel Environment Variables
    - AWS Systems Manager Parameter Store

## Serverless Architecture

-   [Cloudflare Workers Guide](/development/serverless/platforms/cloudflare)
-   [Serverless Best Practices](/development/serverless/core-concepts/best-practices)
-   Edge Network Patterns
-   Lightweight AWS Lambda Configurations
-   Database Integration:
    - Supabase (PostgreSQL)
    - Upstash (Redis)
    - Cloudflare R2 (Object Storage)
    - Cloudflare D1 (SQLite)

## Key Resources
-   Compute (Containers, VMs - For legacy systems)
-   Storage (R2, S3)
-   Networking (CDN, Load Balancers)
