---
title: Learning Astro.js
description: Guide to using Astro for NeXus content projects.
---

# Astro.js Guide

Golden choice to build content sites. 

## Why Astro?

(Brief explanation of benefits - performance, content focus, etc.)

## Key Concepts

-   Project Structure
-   Components (`.astro`)
-   Markdown/MDX Content
-   Layouts
-   Starlight Configuration (`astro.config.mjs`)

## Getting Started

(Steps to set up or work on an Astro project)


## Templates
- Directories

    - [Vercel](https://vercel.com/templates/astro) 维护一堆 template，也可以看看，分类更细致
    - [astro自己的仓库](https://astro.build/themes/) 

- Notable Opensource templates

    - [astrowind](https://github.com/onwidget/astrowind) 号称是 #1 popularity 的模板。AstroJS 5.0+TailwindCSS 3.x 还未支持 Tailwind 4.x， 我手动尝试升级失败
    - [astroship](https://github.com/surjithctly/astroship). as of 25.Apr 支持最新的 astrojs 5.6 + tailwindCSS 4.1 免费版足够简单
    - [ScrewFast](https://github.com/mearashadowfax/ScrewFast). 也是个不错的 boilderplate, 推荐下载了看看，还有些零星 bug, 但可以参考代码。
    - [astrofront](https://github.com/themefisher/astrofront-astro). AstroJs + Shopify + Tailwind CSS + TypeScript Starter and Boilerplate

## Integrations

### Hosting
- [astro.js with cloudflare](https://docs.astro.build/en/guides/integrations-guide/cloudflare/#cloudflare-runtime)

    last time I checked, CF Pages 不支持 sharp 处理 assets, 所以需要对静态内容做定制处理；Vercel 支持 sharp 处理 assets, 但需要付费(?)

### Headless CMS

#### Directus
- [Official Guide from DirectUS](https://directus.io/docs/tutorials/getting-started/fetch-data-from-directus-with-astro)
- [Great boilderplate from Directus team](https://github.com/directus-labs/starters)