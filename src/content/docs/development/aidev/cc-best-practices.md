---
title: <PERSON> Code Best Practices
description: Best practices for using Claude Code
sidebar:
  order: 2
---

# Claude Code Best Practices


# Claude Code Best Practices

## Managing `CLAUDE.md`

The claude-code CLI Memory System: CLAUDE.md

The claude-code command-line tool has a brilliant, file-based memory system built around a special file: CLAUDE.md. <PERSON> automatically reads the contents of this file and includes it in its context at the start of every session in that directory. This provides a persistent, project-specific memory.

There are three main places you can put these memory files:

- Project Memory (./CLAUDE.md): Located in the root of your project. This is the most common location. You should check this file into Git so that project-specific instructions, commands, and style guides are shared with your entire team.
- User Memory (~/.claude/CLAUDE.md): Located in your home directory. The contents of this file are loaded for all your projects. This is perfect for personal preferences, like your preferred coding style or custom tool shortcuts that you use everywhere.
- Parent/Child Directory Memory: Claude recursively looks for CLAUDE.md files. If you're in a subdirectory of a monorepo, it will load the CLAUDE.md from the root and the CLAUDE.md from your current directory. It will also load memory from child directories on-demand as you start interacting with files within them.


Think of your CLAUDE.md file as a carefully crafted prompt that you are constantly refining.

- Be Concise and Structured: Use Markdown headings and bullet points to keep the file readable and organized.
- Document Common Commands: List frequently used build, test, and lint commands. npm run build: Build the project.
- Specify Core Files: Point Claude to key architectural files. Core logic is in src/services/main_service.py.
- Define Code Style: Explicitly state coding conventions. Use ES modules (import/export), not CommonJS (require).
- Explain Project Workflows: Detail processes like Git branching or testing strategies. Always create feature branches from develop.
- Use Imports for Modularity: You can import other files directly into your CLAUDE.md using the @ syntax (e.g., @docs/api_conventions.md). This keeps your main memory file clean and allows for modular context.

You can bootstrap this file by running /init in the claude-code CLI, and you can quickly add new memories during a session by starting your prompt with the # symbol. For more extensive edits, simply use the /memory command to open the file in your default editor.

## Combine the best of Claude and Gemini Pro Models
### 这几个项目各有千秋
- https://github.com/BeehiveInnovations/zen-mcp-server  从 gemini mcp 发展到支持更多的三方高级模型，集成了更复杂的 workflow 和 prompt. 但也臃肿了，tool 太多
- https://github.com/jamubc/gemini-mcp-tool  最新，star 更多些，不知道哪儿做了推广。
- https://github.com/cmdaltctr/claude-gemini-mcp-slim  这个致力于极简化只发挥 Gemini 的协作能力，从描述和 reddit 上作者的发言来看，是最有希望的。

    - Focused exclusively on Gemini AI models.
    - Offers both API access (with a free tier from AI Studio) and a CLI fallback.
    - Provides 3 core tool calls, covering most edge cases; can be extended by chaining with the slash command (see README).
    - Designed for compatibility with any MCP-compatible client via a shared-MCP environment—not limited to Claude Code (CC).
    - When used with CC, supports hooks that automatically trigger actions based on CC events.
    - Features intelligent Gemini model switching based on the task, with customizable options (see README and setup).
    - Includes a slash mode (for Claude Code only) so you don't need to remember available MCP tools or their functions.
    - Allows custom configuration in the MCP JSON to set file size limits, optimizing token usage on the API/CLI free tier for tasks like codebase analysis or security review.

  比较 2、3 我觉得 3 的劣势就是得起个 shared mcp environemnt, 不能即开即用，而且它的配置方法实在有点笨。它这个 mcp environment 应该是为了 fallback 到 gemini cli, 其实没啥必要. 3 的唯一优势直接用 gemin cli 白嫖。
  他们的问题都是没有支持 vertex, 只有 vertex 才能支持更高的 quota 