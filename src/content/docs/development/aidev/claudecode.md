---
title: Claude Code
description: <PERSON> is a mysterious multi-agentic coding assistant by An<PERSON><PERSON>.
sidebar:
  order: 1
---

# <PERSON> Code

## What is <PERSON> Code?

Claude Code is a mysterious multi-agentic coding assistant by <PERSON><PERSON><PERSON>. 说神秘是因为它横空出世，但以"原生"发挥自家模型标志性的编码能力+super long workflow 一直口碑极佳。

## Installation & Configuration

```bash
pnpm install -g @anthropic-ai/claude-code
```
据说之后 claude 内自动更新可能有 bug, 如果届时还没修复就手动更新下。

配置文件在
- `~/.claude/settings.json`
- `~/.claude.json`

基本配置：
- terminal 回车配置。/terminal-setup 可以自动配置为 SHIFT+ENTER 在 prompt 中回车。vscode Terminal/Warp Terminal 里都可以
- 如果要用 opus, 得在 settings.json 里删掉 MAX_TOKEN 的配置
- 可以跟 cursor 等任意 VSC 系的 IDE 配合使用，需安装 Claude Code 的 extension. 

## 使用渠道
- 官方渠道就是订阅 Claude Pro 或者 Max，$20 or $200/month. 每隔 5 小时重置一次 quota。具体 quota 不清晰，按次数和 token 多重计费。总之我看 reddit 上常见说法是 Pro 一天努力牛马可以用掉 $15~25 的价值
- 中转池。一些号商订购几个 MAX 订阅，然后组池子，重新定价出售。完全看号商良心和技术能力了，好的就能无缝支持官网的能力，包括 prompt cache. 

## Facts
- Claude Code auto-compacts context beyond 100k.

## Tools
- [Claudia](https://github.com/getAsterisk/claudia) 是一个 Claude Code 的 GUI, 只是记录下，并不推荐。
- VSC 等 IDE 中 cc 直接以 terminal 形式运行，但如果独立运行 claude code, 需要搭配一个好用的 Terminal. 
    - 通用、全功能型：[Warp Terminal](https://www.warp.dev/)
    - 简洁高效(我的个人选择): [ghostty](https://ghostty.app/)

## References
### Documents and Guides - MUST READS!
- [Famouse Claude Code Log](https://claudelog.com/)
- [Claude Code Best Practices from Anthropic](https://www.anthropic.com/engineering/claude-code-best-practices)
- [Official Docs for Claude Code](https://docs.anthropic.com/en/docs/claude-code/overview)
- [Claude Code Best Tips by steve sewell](https://www.builder.io/blog/claude-code)

### Further Readings
- [Enhancing Claude Code with MCP Servers and Subagents](https://dev.to/oikon/enhancing-claude-code-with-mcp-servers-and-subagents-29dd)

### Cool Stuffs
- [Claude Code Guide](https://github.com/zebbern/claude-code-guide)
- [Awesome Claude Code](https://github.com/hesreallyhim/awesome-claude-code)
- [A set of useful prompts for Claude Code](https://github.com/Wirasm/PRPs-agentic-eng)
- [Claude Code Stuffs](https://github.com/lvalics/claude_code_stuffs)
- [Claude Code Development Kit](https://github.com/peterkrueck/Claude-Code-Development-Kit)