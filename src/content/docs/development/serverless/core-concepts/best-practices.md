---
title: Serverless Best Practices
description: Guidelines for effective serverless development in our stack
---

# Serverless Best Practices

## Core Principles

1. **Platform First** - Default to Cloudflare Workers unless needing:
   - Longer execution time → AWS Lambda
   - Regional presence → GCP Cloud Run
   - Frontend integration → Vercel Serverless

2. **JAMstack Architecture**:
   - Static assets via Cloudflare Pages/Vercel
   - Dynamic elements through serverless APIs
   - Edge caching where possible

3. **Database Integration**:
```mermaid
graph TD
    A[Serverless Function] --> B{Query Type}
    B -->|Simple| C[Cloudflare D1]
    B -->|Redis| D[Upstash]
    B -->|PostgreSQL| E[Supabase]
```

## Cost Control

- Set memory/time limits in Wrangler config
- Enable auto-scaling alerts
- Use Cloudflare's free tier for edge functions
