---
title: NeXus Tech Stack Overview
description: A high-level view of the core technologies used across NeXus projects.
sidebar:
  order: 2
---

# NeXus Tech Stack

This document provides a summary of the primary technologies and platforms we utilize.

## Core Languages

-   TypeScript/JavaScript (Node.js, Frontend)
-   Python (AI/ML, Scripting)

## Frontend

-   Astro
-   React / Next.js (Potentially)
-   Tailwind CSS / UI Libraries

## Backend & Serverless

-   **Primary Platforms**:
    - Cloudflare Workers (Edge computing, APIs)
    - AWS Lambda (Event-driven workflows)
    - Vercel Serverless (Frontend companion functions)
-   **Database Partners**:
    - Supabase (PostgreSQL)
    - Upstash (Redis)
    - Cloudflare D1 (SQLite)
-   **Infrastructure Tools**:
    - Wrangler (Cloudflare deployment)
    - Terraform (Multi-cloud configuration)
    - Serverless Framework (AWS Lambda)

## AI/ML

-   OpenAI APIs
-   Hugging Face Models/Libraries
-   Vector Databases (e.g., Pinecone, Cloudflare Vectorize)

## Databases

-   PostgreSQL (Supabase, Neon)
-   Redis
-   (Other NoSQL/Specialized DBs as needed)

## Infrastructure & Ops

-   Cloudflare (Workers, Pages, R2, etc.)
-   AWS (Select services)
-   Terraform
-   Docker
