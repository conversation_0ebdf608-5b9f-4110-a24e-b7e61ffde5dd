# S3-compatible Storage Configuration
# This file serves as a template - copy to .env and fill with your values

# Public bucket URL (required)
PUBLIC_S3_BUCKET_URL="https://your-bucket-name.your-storage-provider.com"

# S3 Credentials (required for uploading)
S3_REGION="auto"                       # Use "auto" for Cloudflare R2, or specific region for AWS S3
S3_ENDPOINT="https://your-endpoint"    # For R2: https://{account_id}.r2.cloudflarestorage.com
S3_ACCESS_KEY_ID="your-access-key-id"
S3_SECRET_ACCESS_KEY="your-secret-access-key"
S3_BUCKET_NAME="your-bucket-name"

# Folder Structure Configuration
S3_DEFAULT_PREFIX="nexus-docs/"              # Default prefix for all uploads (e.g., "images/")
S3_USE_TIME_PREFIX="true"              # Whether to use YYmm folders (true/false)

# Storage Provider Specific Settings
# Uncomment the section relevant to your provider:

# Cloudflare R2 Configuration
# R2_ACCOUNT_ID="your-account-id"

# AWS S3 Configuration
# AWS_PROFILE="default"                # Optional: if using AWS credentials file 